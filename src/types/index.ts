export interface AccountInfo {
  id: string;
  email: string;
  registered: boolean;
  promotional: boolean;
  sold: boolean;
  registrationDate: string;
  buyerEmail?: string;
}

export interface PaymentInfo {
  orderId: string;
  amount: number;
  qrCode: string;
  expireTime: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
}

export interface StockInfo {
  remaining: number;
  sold: number;
  currentPrice: number;
}
import React from 'react';
import { Mail, MessageCircle, AlertCircle } from 'lucide-react';

export const Footer: React.FC = () => {
  return (
    <footer className="mt-20 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-10 shadow-2xl">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center space-x-3">
            <div className="p-2 bg-blue-500/20 backdrop-blur-sm rounded-lg border border-blue-300/30">
              <AlertCircle className="h-5 w-5 text-blue-600" />
            </div>
            <span>购买须知</span>
          </h3>
          <ul className="space-y-3 text-sm text-gray-700 leading-relaxed">
            <li>• 账号为正版 Augment 账号，保证可正常使用</li>
            <li>• 支付成功后账号信息将发送至您的邮箱</li>
            <li>• 请确保邮箱地址正确，避免无法接收账号</li>
            <li>• 账号售出后不支持退款，请谨慎购买</li>
            <li>• 建议及时修改账号密码以确保安全</li>
          </ul>
        </div>
        
        <div>
          <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center space-x-3">
            <div className="p-2 bg-green-500/20 backdrop-blur-sm rounded-lg border border-green-300/30">
              <MessageCircle className="h-5 w-5 text-green-600" />
            </div>
            <span>联系我们</span>
          </h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-3 p-3 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
              <Mail className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-700 font-medium"><EMAIL></span>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
              <MessageCircle className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-700 font-medium">微信客服：augment_service</span>
            </div>
          </div>
          
          <div className="mt-8 p-6 bg-gradient-to-r from-blue-500/10 to-purple-500/10 backdrop-blur-sm rounded-xl border border-white/20">
            <p className="text-sm text-blue-900 font-medium leading-relaxed">
              <strong>温馨提示：</strong>如有任何问题，请及时联系客服，我们将竭诚为您服务。
            </p>
          </div>
        </div>
      </div>
      
      <div className="mt-10 pt-8 border-t border-white/20 text-center">
        <p className="text-sm text-gray-600 font-medium">
          © 2024 Augment 账号售卖平台. All rights reserved.
        </p>
      </div>
    </footer>
  );
};
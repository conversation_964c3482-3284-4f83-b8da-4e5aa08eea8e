import React from 'react';
import { Package, TrendingUp, Users, ShoppingCart } from 'lucide-react';
import { StockInfo } from '../types';

interface StockIndicatorProps {
  stockInfo: StockInfo;
  loading: boolean;
}

export const StockIndicator: React.FC<StockIndicatorProps> = ({ stockInfo, loading }) => {
  if (loading) {
    return (
      <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-8 shadow-2xl">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-white/20 rounded-xl w-1/2"></div>
          <div className="h-12 bg-white/20 rounded-xl w-3/4"></div>
          <div className="h-32 bg-white/20 rounded-xl w-full"></div>
        </div>
      </div>
    );
  }

  const isOutOfStock = stockInfo.remaining === 0;

  return (
    <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-8 shadow-2xl hover:bg-white/15 transition-all duration-300">
      <div className="mb-8">
        <h3 className="text-2xl font-bold text-gray-900 mb-2 flex items-center space-x-3">
          <Package className="h-6 w-6 text-blue-600" />
          <span>库存状态</span>
        </h3>
        <p className="text-gray-600">实时更新的账号库存信息</p>
      </div>
      
      <div className="grid grid-cols-1 gap-6 mb-8">
        <div className="group bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6 hover:bg-white/15 transition-all duration-300">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className={`p-4 rounded-xl backdrop-blur-sm border ${
                isOutOfStock 
                  ? 'bg-red-500/20 border-red-300/30' 
                  : 'bg-blue-500/20 border-blue-300/30'
              }`}>
            <Package className={`h-6 w-6 ${isOutOfStock ? 'text-red-600' : 'text-blue-600'}`} />
          </div>
              <div>
                <p className="text-sm text-gray-600 font-medium">剩余库存</p>
                <p className={`text-3xl font-bold ${isOutOfStock ? 'text-red-600' : 'text-blue-600'}`}>
              {stockInfo.remaining}
            </p>
                <p className="text-xs text-gray-500 mt-1">个可用账号</p>
          </div>
            </div>
            <div className={`px-4 py-2 rounded-full text-sm font-medium ${
              isOutOfStock 
                ? 'bg-red-500/20 text-red-700 border border-red-300/30' 
                : 'bg-green-500/20 text-green-700 border border-green-300/30'
            }`}>
              {isOutOfStock ? '缺货' : '有货'}
            </div>
          </div>
        </div>
        
        <div className="group bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6 hover:bg-white/15 transition-all duration-300">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-4 rounded-xl bg-green-500/20 backdrop-blur-sm border border-green-300/30">
            <TrendingUp className="h-6 w-6 text-green-600" />
          </div>
              <div>
                <p className="text-sm text-gray-600 font-medium">已售数量</p>
                <p className="text-3xl font-bold text-green-600">{stockInfo.sold}</p>
                <p className="text-xs text-gray-500 mt-1">个已售出</p>
          </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">销售率</p>
              <p className="text-lg font-bold text-gray-900">
                {stockInfo.sold + stockInfo.remaining > 0 
                  ? Math.round((stockInfo.sold / (stockInfo.sold + stockInfo.remaining)) * 100)
                  : 0}%
              </p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Sales Statistics */}
      <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 backdrop-blur-sm border border-white/20 rounded-xl p-6 mb-6">
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Users className="h-5 w-5 text-blue-600 mr-2" />
              <span className="text-sm font-medium text-gray-700">总用户</span>
            </div>
            <p className="text-2xl font-bold text-blue-600">{stockInfo.sold}</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <ShoppingCart className="h-5 w-5 text-purple-600 mr-2" />
              <span className="text-sm font-medium text-gray-700">总库存</span>
            </div>
            <p className="text-2xl font-bold text-purple-600">{stockInfo.sold + stockInfo.remaining}</p>
          </div>
        </div>
      </div>
      
      {isOutOfStock && (
        <div className="bg-red-500/10 backdrop-blur-sm border border-red-300/30 rounded-xl p-4">
          <p className="text-red-700 text-sm font-medium text-center flex items-center justify-center space-x-2">
            <Package className="h-4 w-4" />
            <span>
            📦 当前库存已售罄，正在紧急补货中...
            </span>
          </p>
        </div>
      )}
      
      {!isOutOfStock && (
        <div className="bg-green-500/10 backdrop-blur-sm border border-green-300/30 rounded-xl p-4">
          <p className="text-green-700 text-sm font-medium text-center flex items-center justify-center space-x-2">
            <Package className="h-4 w-4" />
            <span>✅ 库存充足，支持立即购买</span>
          </p>
        </div>
      )}
    </div>
  );
};
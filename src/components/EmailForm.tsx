import React, { useState } from 'react';
import { Mail, ArrowRight, Shield, Zap } from 'lucide-react';

interface EmailFormProps {
  onSubmit: (email: string) => void;
  loading: boolean;
  disabled: boolean;
  currentPrice: number;
}

export const EmailForm: React.FC<EmailFormProps> = ({ 
  onSubmit, 
  loading, 
  disabled, 
  currentPrice 
}) => {
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim()) {
      setEmailError('请输入邮箱地址');
      return;
    }
    
    if (!validateEmail(email)) {
      setEmailError('请输入有效的邮箱地址');
      return;
    }
    
    setEmailError('');
    onSubmit(email);
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    if (emailError) {
      setEmailError('');
    }
  };

  return (
    <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-8 shadow-2xl hover:bg-white/15 transition-all duration-300">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-white/30 rounded-2xl mb-6 shadow-lg">
          <Mail className="h-10 w-10 text-blue-600" />
        </div>
        <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-blue-900 bg-clip-text text-transparent mb-4">购买 Augment 账号</h2>
        <p className="text-gray-700 leading-relaxed">
          输入您的邮箱地址，我们将在支付成功后发送账号信息
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="email" className="block text-sm font-bold text-gray-800 mb-3">
            接收邮箱 *
          </label>
          <div className="relative">
            <input
              type="email"
              id="email"
              value={email}
              onChange={handleEmailChange}
              placeholder="请输入您的邮箱地址"
              className={`w-full px-4 py-4 bg-white/10 backdrop-blur-sm border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-400 transition-all duration-300 text-gray-900 placeholder-gray-500 ${
                emailError 
                  ? 'border-red-400/50 bg-red-500/10' 
                  : 'border-white/30 hover:border-white/40 focus:bg-white/15'
              }`}
              disabled={loading || disabled}
            />
            <Mail className="absolute right-4 top-4 h-5 w-5 text-gray-500" />
          </div>
          {emailError && (
            <p className="mt-3 text-sm text-red-600 font-medium">{emailError}</p>
          )}
        </div>

        <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 backdrop-blur-sm border border-white/20 rounded-xl p-6">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <Zap className="h-5 w-5 text-blue-600" />
              <span className="text-gray-700 font-medium">单价</span>
            </div>
            <span className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">¥{currentPrice}</span>
          </div>
        </div>

        {/* Security Features */}
        <div className="bg-white/5 backdrop-blur-sm border border-white/20 rounded-xl p-4">
          <div className="flex items-center space-x-3 mb-3">
            <Shield className="h-5 w-5 text-green-600" />
            <span className="text-sm font-bold text-gray-800">安全保障</span>
          </div>
          <div className="grid grid-cols-2 gap-3 text-xs text-gray-600">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>正版账号</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>即时发货</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span>安全支付</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
              <span>邮件通知</span>
            </div>
          </div>
        </div>

        <button
          type="submit"
          disabled={loading || disabled || !email.trim()}
          className={`w-full py-4 px-6 rounded-xl font-bold text-white transition-all duration-300 flex items-center justify-center space-x-3 shadow-lg ${
            disabled
              ? 'bg-gray-500/50 cursor-not-allowed backdrop-blur-sm'
              : loading
              ? 'bg-blue-500/70 cursor-wait backdrop-blur-sm'
              : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 active:transform active:scale-95 hover:shadow-2xl backdrop-blur-sm'
          }`}
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
              <span>创建订单中...</span>
            </>
          ) : disabled ? (
            <span>暂时缺货</span>
          ) : (
            <>
              <span>立即购买</span>
              <ArrowRight className="h-6 w-6" />
            </>
          )}
        </button>
      </form>

      <div className="mt-6 text-center">
        <p className="text-xs text-gray-600">
          * 支付成功后，账号信息将自动发送至您的邮箱
        </p>
      </div>
    </div>
  );
};
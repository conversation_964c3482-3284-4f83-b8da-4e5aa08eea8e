import React, { useState, useEffect } from 'react';
import { X, Clock, CheckCircle, AlertCircle, RefreshCw } from 'lucide-react';
import { PaymentInfo } from '../types';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  paymentInfo: PaymentInfo;
  onPaymentSuccess: () => void;
  onCheckStatus: (orderId: string) => Promise<boolean>;
}

export const PaymentModal: React.FC<PaymentModalProps> = ({
  isOpen,
  onClose,
  paymentInfo,
  onPaymentSuccess,
  onCheckStatus
}) => {
  const [timeLeft, setTimeLeft] = useState(0);
  const [checking, setChecking] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState<'pending' | 'success' | 'expired'>('pending');

  useEffect(() => {
    if (!isOpen) return;

    const updateTimeLeft = () => {
      const remaining = Math.max(0, paymentInfo.expireTime - Date.now());
      setTimeLeft(remaining);
      
      if (remaining === 0) {
        setPaymentStatus('expired');
      }
    };

    updateTimeLeft();
    const timer = setInterval(updateTimeLeft, 1000);

    return () => clearInterval(timer);
  }, [isOpen, paymentInfo.expireTime]);

  const formatTime = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleCheckStatus = async () => {
    if (checking) return;
    
    setChecking(true);
    try {
      const isPaid = await onCheckStatus(paymentInfo.orderId);
      if (isPaid) {
        setPaymentStatus('success');
        setTimeout(() => {
          onPaymentSuccess();
        }, 2000);
      }
    } catch (error) {
      console.error('Check payment status failed:', error);
    } finally {
      setChecking(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-md z-50 flex items-center justify-center p-4">
      <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl shadow-2xl w-full max-w-md relative animate-in fade-in duration-300">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 p-2 hover:bg-white/20 rounded-full transition-all duration-200 backdrop-blur-sm"
        >
          <X className="h-5 w-5 text-gray-700" />
        </button>

        <div className="p-8">
          {paymentStatus === 'success' ? (
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-green-500/20 backdrop-blur-sm border border-green-300/30 rounded-2xl mb-6">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">支付成功！</h3>
              <p className="text-gray-700 mb-8 leading-relaxed">
                账号信息已发送至您的邮箱，请注意查收
              </p>
              <div className="animate-pulse">
                <div className="h-3 bg-gradient-to-r from-green-400 to-blue-400 rounded-full"></div>
              </div>
            </div>
          ) : paymentStatus === 'expired' ? (
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-red-500/20 backdrop-blur-sm border border-red-300/30 rounded-2xl mb-6">
                <AlertCircle className="h-8 w-8 text-red-600" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">支付已过期</h3>
              <p className="text-gray-700 mb-8 leading-relaxed">
                支付时间已超时，请重新创建订单
              </p>
              <button
                onClick={onClose}
                className="w-full py-4 px-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 font-bold shadow-lg"
              >
                重新购买
              </button>
            </div>
          ) : (
            <>
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">微信支付</h3>
                <p className="text-gray-700 leading-relaxed">请使用微信扫描下方二维码完成支付</p>
              </div>

              <div className="flex justify-center mb-6">
                <div className="bg-white/20 backdrop-blur-sm p-6 rounded-2xl border border-white/30 shadow-xl">
                  <img
                    src={paymentInfo.qrCode}
                    alt="微信支付二维码"
                    className="w-48 h-48 rounded-xl"
                  />
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6 mb-6">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-700 font-medium">订单金额</span>
                  <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">¥{paymentInfo.amount}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-700 font-medium">订单编号</span>
                  <span className="text-sm text-gray-600 font-mono bg-white/10 px-2 py-1 rounded">{paymentInfo.orderId}</span>
                </div>
              </div>

              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-2">
                  <Clock className="h-5 w-5 text-orange-600" />
                  <span className="text-gray-700 font-medium">剩余时间</span>
                </div>
                <span className={`font-mono text-lg font-bold ${
                  timeLeft < 300000 ? 'text-red-600' : 'text-orange-600'
                }`}>
                  {formatTime(timeLeft)}
                </span>
              </div>

              <button
                onClick={handleCheckStatus}
                disabled={checking}
                className="w-full py-4 px-6 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 disabled:from-green-400 disabled:to-emerald-400 transition-all duration-300 flex items-center justify-center space-x-3 font-bold shadow-lg backdrop-blur-sm"
              >
                {checking ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>检查中...</span>
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4" />
                    <span>我已支付，检查状态</span>
                  </>
                )}
              </button>

              <p className="text-xs text-gray-600 text-center mt-6">
                支付完成后点击上方按钮或等待自动确认
              </p>
            </>
          )}
        </div>
      </div>
    </div>
  );
};
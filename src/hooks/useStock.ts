import { useState, useEffect } from 'react';
import { StockInfo } from '../types';

export const useStock = () => {
  const [stockInfo, setStockInfo] = useState<StockInfo>({
    remaining: 0,
    sold: 0,
    currentPrice: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStock = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Mock API call - replace with actual API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simulated stock data
      setStockInfo({
        remaining: Math.floor(Math.random() * 50) + 1,
        sold: Math.floor(Math.random() * 100),
        currentPrice: 29.99
      });
    } catch (err) {
      setError('获取库存信息失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStock();
    // Refresh stock every 30 seconds
    const interval = setInterval(fetchStock, 30000);
    return () => clearInterval(interval);
  }, []);

  return { stockInfo, loading, error, refetch: fetchStock };
};
import { useState } from 'react';
import { PaymentInfo } from '../types';

// API配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

export const usePayment = () => {
  const [paymentInfo, setPaymentInfo] = useState<PaymentInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createPayment = async (email: string) => {
    try {
      setLoading(true);
      setError(null);

      // 调用真实的支付API
      const response = await fetch(`${API_BASE_URL}/api/payment/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          amount: 29.99
        })
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.error || '创建支付订单失败');
      }

      const paymentData: PaymentInfo = {
        orderId: data.data.orderId,
        amount: data.data.amount,
        qrCode: data.data.qrCode,
        expireTime: data.data.expireTime
      };

      setPaymentInfo(paymentData);
      return paymentData;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '创建支付订单失败，请重试';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const checkPaymentStatus = async (orderId: string) => {
    try {
      // 调用真实的支付状态查询API
      const response = await fetch(`${API_BASE_URL}/api/payment/status/${orderId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.error || '查询支付状态失败');
      }

      // 返回支付是否成功
      return data.data.paid === true;
    } catch (err) {
      console.error('检查支付状态失败:', err);
      setError('检查支付状态失败');
      return false;
    }
  };

  return {
    paymentInfo,
    loading,
    error,
    createPayment,
    checkPaymentStatus,
    clearPayment: () => setPaymentInfo(null)
  };
};
import { useState } from 'react';
import { PaymentInfo } from '../types';

export const usePayment = () => {
  const [paymentInfo, setPaymentInfo] = useState<PaymentInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createPayment = async (email: string) => {
    try {
      setLoading(true);
      setError(null);

      // Mock API call - replace with actual payment API
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Simulated payment creation
      const mockPayment: PaymentInfo = {
        orderId: `ORDER_${Date.now()}`,
        amount: 29.99,
        qrCode: 'https://via.placeholder.com/200x200/1f2937/ffffff?text=WeChat+Pay+QR',
        expireTime: Date.now() + 15 * 60 * 1000 // 15 minutes
      };

      setPaymentInfo(mockPayment);
      return mockPayment;
    } catch (err) {
      setError('创建支付订单失败，请重试');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const checkPaymentStatus = async (orderId: string) => {
    try {
      // Mock payment status check
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simulate random payment success/failure
      return Math.random() > 0.3;
    } catch (err) {
      setError('检查支付状态失败');
      return false;
    }
  };

  return {
    paymentInfo,
    loading,
    error,
    createPayment,
    checkPaymentStatus,
    clearPayment: () => setPaymentInfo(null)
  };
};
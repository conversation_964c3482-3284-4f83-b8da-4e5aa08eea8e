import React, { useState } from 'react';
import { Header } from './components/Header';
import { StockIndicator } from './components/StockIndicator';
import { EmailForm } from './components/EmailForm';
import { PaymentModal } from './components/PaymentModal';
import { Footer } from './components/Footer';
import { useStock } from './hooks/useStock';
import { usePayment } from './hooks/usePayment';

function App() {
  const { stockInfo, loading: stockLoading, error: stockError } = useStock();
  const { 
    paymentInfo, 
    loading: paymentLoading, 
    error: paymentError, 
    createPayment, 
    checkPaymentStatus, 
    clearPayment 
  } = usePayment();
  
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [purchaseComplete, setPurchaseComplete] = useState(false);

  const handleEmailSubmit = async (email: string) => {
    try {
      await createPayment(email);
      setShowPaymentModal(true);
    } catch (error) {
      console.error('Payment creation failed:', error);
    }
  };

  const handlePaymentSuccess = () => {
    setShowPaymentModal(false);
    setPurchaseComplete(true);
    clearPayment();
    
    // Reset after 5 seconds
    setTimeout(() => {
      setPurchaseComplete(false);
    }, 5000);
  };

  const handleClosePaymentModal = () => {
    setShowPaymentModal(false);
    clearPayment();
  };

  if (purchaseComplete) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-100 via-purple-50 to-pink-100 flex items-center justify-center p-4 relative overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-green-400/30 to-blue-400/30 rounded-full blur-3xl -translate-x-1/2 -translate-y-1/2"></div>
          <div className="absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-br from-purple-400/30 to-pink-400/30 rounded-full blur-3xl translate-x-1/2 translate-y-1/2"></div>
        </div>
        
        <div className="text-center bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-16 shadow-2xl max-w-lg w-full relative z-10">
          <div className="inline-flex items-center justify-center w-24 h-24 bg-green-500/20 backdrop-blur-sm border border-green-300/30 rounded-2xl mb-8">
            <svg className="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 className="text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-6">购买成功！</h2>
          <p className="text-gray-700 mb-8 text-lg leading-relaxed">
            Augment 账号信息已发送至您的邮箱，请注意查收。
          </p>
          <p className="text-sm text-gray-600 font-medium">
            页面将在几秒后自动刷新...
          </p>
          <div className="mt-8">
            <div className="h-2 bg-gradient-to-r from-green-400 via-blue-400 to-purple-400 rounded-full animate-pulse"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-100 via-purple-50 to-pink-100 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-400/30 to-purple-400/30 rounded-full blur-3xl -translate-x-1/2 -translate-y-1/2"></div>
        <div className="absolute top-1/3 right-0 w-80 h-80 bg-gradient-to-br from-pink-400/30 to-orange-400/30 rounded-full blur-3xl translate-x-1/2"></div>
        <div className="absolute bottom-0 left-1/3 w-72 h-72 bg-gradient-to-br from-green-400/30 to-blue-400/30 rounded-full blur-3xl translate-y-1/2"></div>
        <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl -translate-x-1/2 -translate-y-1/2"></div>
      </div>
      
      <div className="container mx-auto px-4 py-12 max-w-6xl relative z-10">
        <Header />
        
        {stockError && (
          <div className="mb-12 p-6 bg-red-500/10 backdrop-blur-md border border-red-300/30 rounded-2xl shadow-lg">
            <p className="text-red-800 text-center font-medium">{stockError}</p>
          </div>
        )}
        
        {paymentError && (
          <div className="mb-12 p-6 bg-red-500/10 backdrop-blur-md border border-red-300/30 rounded-2xl shadow-lg">
            <p className="text-red-800 text-center font-medium">{paymentError}</p>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          <StockIndicator stockInfo={stockInfo} loading={stockLoading} />
          
          <EmailForm
            onSubmit={handleEmailSubmit}
            loading={paymentLoading}
            disabled={stockInfo.remaining === 0}
            currentPrice={stockInfo.currentPrice}
          />
        </div>

        <Footer />
      </div>

      {paymentInfo && (
        <PaymentModal
          isOpen={showPaymentModal}
          onClose={handleClosePaymentModal}
          paymentInfo={paymentInfo}
          onPaymentSuccess={handlePaymentSuccess}
          onCheckStatus={checkPaymentStatus}
        />
      )}
    </div>
  );
}

export default App;
import { Router } from 'express';
import PaymentController from '../controllers/PaymentController';
import { validateCreateOrder, validateOrderId } from '../middleware/validation';
import { rateLimiter } from '../middleware/rateLimiter';

const router = Router();

// 创建支付订单
router.post('/create', 
  rateLimiter.createOrder,
  validateCreateOrder,
  PaymentController.createOrder
);

// 查询订单状态
router.get('/status/:orderId',
  rateLimiter.queryStatus,
  validateOrderId,
  PaymentController.getOrderStatus
);

// 微信支付回调
router.post('/notify',
  PaymentController.handleWechatNotify
);

// 管理接口：获取订单列表
router.get('/orders',
  rateLimiter.adminApi,
  PaymentController.getOrders
);

export default router;

import { v4 as uuidv4 } from 'uuid';
import Order from '../models/Order';
import { getWechatPayment, wechatConfig, PAYMENT_CONFIG } from '../config/wechat';
import { OrderStatus, CreateOrderRequest, CreateOrderResponse, WechatOrderQueryResponse } from '../types';

export class PaymentService {
  private wechatPay = getWechatPayment();

  /**
   * 创建支付订单
   */
  async createOrder(request: CreateOrderRequest): Promise<CreateOrderResponse> {
    try {
      // 生成订单ID
      const orderId = this.generateOrderId();
      
      // 计算过期时间
      const expireTime = new Date();
      expireTime.setMinutes(expireTime.getMinutes() + PAYMENT_CONFIG.TIMEOUT_MINUTES);

      // 创建微信支付订单
      const wechatOrderData = {
        appid: wechatConfig.appid,
        mchid: wechatConfig.mchid,
        description: PAYMENT_CONFIG.DESCRIPTION,
        out_trade_no: orderId,
        notify_url: wechatConfig.notifyUrl,
        amount: {
          total: Math.round(request.amount * 100), // 转换为分
          currency: 'CNY'
        },
        attach: PAYMENT_CONFIG.ATTACH,
        time_expire: expireTime.toISOString().replace(/\.\d{3}Z$/, '+08:00')
      };

      console.log('创建微信支付订单:', wechatOrderData);

      // 调用微信支付 Native API
      const wechatResponse = await this.wechatPay.native(wechatOrderData);
      
      if (!wechatResponse.code_url) {
        throw new Error('微信支付返回的二维码URL为空');
      }

      // 保存订单到数据库
      const order = await Order.create({
        id: orderId,
        email: request.email,
        amount: request.amount,
        status: OrderStatus.PENDING,
        qrCodeUrl: wechatResponse.code_url,
        expireTime: expireTime
      });

      console.log('订单创建成功:', order.id);

      return {
        orderId: order.id,
        qrCode: order.qrCodeUrl!,
        amount: order.amount,
        expireTime: expireTime.getTime()
      };

    } catch (error) {
      console.error('创建支付订单失败:', error);
      throw new Error(`创建支付订单失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 查询订单状态
   */
  async getOrderStatus(orderId: string): Promise<{ status: OrderStatus; paid?: boolean }> {
    try {
      const order = await Order.findByPk(orderId);
      
      if (!order) {
        throw new Error('订单不存在');
      }

      // 如果订单已过期，更新状态
      if (order.isExpired() && order.status === OrderStatus.PENDING) {
        await order.update({ status: OrderStatus.EXPIRED });
        return { status: OrderStatus.EXPIRED, paid: false };
      }

      // 如果订单状态是待支付，查询微信支付状态
      if (order.status === OrderStatus.PENDING) {
        const wechatStatus = await this.queryWechatOrderStatus(orderId);
        
        if (wechatStatus.trade_state === 'SUCCESS') {
          // 支付成功，更新订单状态
          await order.update({ 
            status: OrderStatus.PAID,
            wechatOrderId: wechatStatus.transaction_id 
          });
          
          // 这里可以添加发送邮件等后续处理
          await this.handlePaymentSuccess(order);
          
          return { status: OrderStatus.PAID, paid: true };
        }
      }

      return { 
        status: order.status, 
        paid: order.status === OrderStatus.PAID 
      };

    } catch (error) {
      console.error('查询订单状态失败:', error);
      throw new Error(`查询订单状态失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 查询微信支付订单状态
   */
  private async queryWechatOrderStatus(orderId: string): Promise<WechatOrderQueryResponse> {
    try {
      const response = await this.wechatPay.query({
        out_trade_no: orderId
      });
      
      return response as WechatOrderQueryResponse;
    } catch (error) {
      console.error('查询微信支付状态失败:', error);
      throw error;
    }
  }

  /**
   * 处理支付成功后的业务逻辑
   */
  private async handlePaymentSuccess(order: Order): Promise<void> {
    try {
      console.log(`订单 ${order.id} 支付成功，邮箱: ${order.email}`);
      
      // TODO: 这里添加具体的业务逻辑
      // 1. 发送包含账号信息的邮件
      // 2. 更新库存
      // 3. 记录销售日志
      // 4. 其他后续处理
      
    } catch (error) {
      console.error('处理支付成功逻辑失败:', error);
      // 注意：这里不要抛出错误，避免影响支付状态更新
    }
  }

  /**
   * 生成订单ID
   */
  private generateOrderId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `${PAYMENT_CONFIG.ORDER_PREFIX}${timestamp}_${random}`;
  }

  /**
   * 处理微信支付回调
   */
  async handleWechatNotify(notificationData: any): Promise<void> {
    try {
      // TODO: 实现微信支付回调处理
      // 1. 验证签名
      // 2. 解密数据
      // 3. 更新订单状态
      // 4. 返回成功响应
      
      console.log('收到微信支付回调:', notificationData);
      
    } catch (error) {
      console.error('处理微信支付回调失败:', error);
      throw error;
    }
  }
}

export default new PaymentService();

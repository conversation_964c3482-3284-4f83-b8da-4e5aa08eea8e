// 订单状态枚举
export enum OrderStatus {
  PENDING = 'pending',
  PAID = 'paid',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled'
}

// 订单接口
export interface Order {
  id: string;
  email: string;
  amount: number;
  status: OrderStatus;
  wechatOrderId?: string;
  qrCodeUrl?: string;
  expireTime: Date;
  createdAt: Date;
  updatedAt: Date;
}

// 创建订单请求
export interface CreateOrderRequest {
  email: string;
  amount: number;
}

// 创建订单响应
export interface CreateOrderResponse {
  orderId: string;
  qrCode: string;
  amount: number;
  expireTime: number;
}

// API 响应格式
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 微信支付回调数据
export interface WechatPayNotification {
  id: string;
  create_time: string;
  event_type: string;
  resource_type: string;
  resource: {
    ciphertext: string;
    associated_data: string;
    nonce: string;
  };
}

// 微信支付订单查询响应
export interface WechatOrderQueryResponse {
  appid: string;
  mchid: string;
  out_trade_no: string;
  transaction_id?: string;
  trade_type: string;
  trade_state: string;
  trade_state_desc: string;
  bank_type?: string;
  attach?: string;
  success_time?: string;
  payer: {
    openid: string;
  };
  amount: {
    total: number;
    payer_total: number;
    currency: string;
    payer_currency: string;
  };
}

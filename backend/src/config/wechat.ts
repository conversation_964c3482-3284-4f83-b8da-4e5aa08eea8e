import { Payment } from 'wechatpay-node-v3';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

dotenv.config();

// 微信支付配置
export const wechatConfig = {
  appid: process.env.WECHAT_APPID!,
  mchid: process.env.WECHAT_MCHID!,
  privateKeyPath: process.env.WECHAT_PRIVATE_KEY_PATH!,
  certificateSerialNumber: process.env.WECHAT_CERTIFICATE_SERIAL_NUMBER!,
  apiV3Key: process.env.WECHAT_API_V3_KEY!,
  notifyUrl: `${process.env.API_BASE_URL}/api/payment/notify`,
};

// 验证配置
function validateConfig() {
  const requiredFields = [
    'WECHAT_APPID',
    'WECHAT_MCHID', 
    'WECHAT_PRIVATE_KEY_PATH',
    'WECHAT_CERTIFICATE_SERIAL_NUMBER',
    'WECHAT_API_V3_KEY'
  ];

  const missingFields = requiredFields.filter(field => !process.env[field]);
  
  if (missingFields.length > 0) {
    throw new Error(`缺少微信支付配置: ${missingFields.join(', ')}`);
  }

  // 检查私钥文件是否存在
  const privateKeyPath = path.resolve(wechatConfig.privateKeyPath);
  if (!fs.existsSync(privateKeyPath)) {
    throw new Error(`微信支付私钥文件不存在: ${privateKeyPath}`);
  }
}

// 创建微信支付实例
let paymentInstance: Payment | null = null;

export function getWechatPayment(): Payment {
  if (!paymentInstance) {
    try {
      validateConfig();
      
      const privateKey = fs.readFileSync(path.resolve(wechatConfig.privateKeyPath));
      
      paymentInstance = new Payment({
        appid: wechatConfig.appid,
        mchid: wechatConfig.mchid,
        publicKey: privateKey, // 这里实际上是私钥
        key: wechatConfig.apiV3Key,
      });
      
      console.log('微信支付 SDK 初始化成功');
    } catch (error) {
      console.error('微信支付 SDK 初始化失败:', error);
      throw error;
    }
  }
  
  return paymentInstance;
}

// 支付配置常量
export const PAYMENT_CONFIG = {
  TIMEOUT_MINUTES: parseInt(process.env.PAYMENT_TIMEOUT_MINUTES || '15'),
  ORDER_PREFIX: process.env.ORDER_PREFIX || 'ORDER_',
  DESCRIPTION: 'Augment账号购买',
  ATTACH: 'augment-account-purchase'
};

import { Request, Response } from 'express';
import PaymentService from '../services/PaymentService';
import { CreateOrderRequest, ApiResponse } from '../types';

export class PaymentController {
  /**
   * 创建支付订单
   */
  async createOrder(req: Request, res: Response): Promise<void> {
    try {
      const { email, amount }: CreateOrderRequest = req.body;

      // 参数验证
      if (!email || !amount) {
        res.status(400).json({
          success: false,
          error: '邮箱和金额不能为空'
        } as ApiResponse);
        return;
      }

      // 邮箱格式验证
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        res.status(400).json({
          success: false,
          error: '邮箱格式不正确'
        } as ApiResponse);
        return;
      }

      // 金额验证
      if (amount <= 0 || amount > 10000) {
        res.status(400).json({
          success: false,
          error: '金额必须在0.01-10000之间'
        } as ApiResponse);
        return;
      }

      const orderData = await PaymentService.createOrder({ email, amount });

      res.json({
        success: true,
        data: orderData
      } as ApiResponse);

    } catch (error) {
      console.error('创建订单失败:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : '创建订单失败'
      } as ApiResponse);
    }
  }

  /**
   * 查询订单状态
   */
  async getOrderStatus(req: Request, res: Response): Promise<void> {
    try {
      const { orderId } = req.params;

      if (!orderId) {
        res.status(400).json({
          success: false,
          error: '订单ID不能为空'
        } as ApiResponse);
        return;
      }

      const statusData = await PaymentService.getOrderStatus(orderId);

      res.json({
        success: true,
        data: statusData
      } as ApiResponse);

    } catch (error) {
      console.error('查询订单状态失败:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : '查询订单状态失败'
      } as ApiResponse);
    }
  }

  /**
   * 微信支付回调
   */
  async handleWechatNotify(req: Request, res: Response): Promise<void> {
    try {
      const notificationData = req.body;
      
      await PaymentService.handleWechatNotify(notificationData);

      // 返回微信要求的成功响应格式
      res.status(200).json({
        code: 'SUCCESS',
        message: '成功'
      });

    } catch (error) {
      console.error('处理微信支付回调失败:', error);
      
      // 返回微信要求的失败响应格式
      res.status(500).json({
        code: 'FAIL',
        message: error instanceof Error ? error.message : '处理失败'
      });
    }
  }

  /**
   * 获取订单列表（管理接口）
   */
  async getOrders(req: Request, res: Response): Promise<void> {
    try {
      // TODO: 实现订单列表查询
      // 这里可以添加分页、筛选等功能
      
      res.json({
        success: true,
        data: {
          orders: [],
          total: 0,
          page: 1,
          pageSize: 10
        }
      } as ApiResponse);

    } catch (error) {
      console.error('获取订单列表失败:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : '获取订单列表失败'
      } as ApiResponse);
    }
  }
}

export default new PaymentController();

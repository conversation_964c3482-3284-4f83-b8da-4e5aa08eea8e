import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '../types';

/**
 * 验证创建订单请求
 */
export const validateCreateOrder = (req: Request, res: Response, next: NextFunction): void => {
  const { email, amount } = req.body;

  // 检查必填字段
  if (!email || amount === undefined || amount === null) {
    res.status(400).json({
      success: false,
      error: '邮箱和金额不能为空'
    } as ApiResponse);
    return;
  }

  // 邮箱格式验证
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    res.status(400).json({
      success: false,
      error: '邮箱格式不正确'
    } as ApiResponse);
    return;
  }

  // 金额验证
  const numAmount = Number(amount);
  if (isNaN(numAmount) || numAmount <= 0 || numAmount > 10000) {
    res.status(400).json({
      success: false,
      error: '金额必须是0.01-10000之间的数字'
    } as ApiResponse);
    return;
  }

  // 金额精度验证（最多2位小数）
  if (!/^\d+(\.\d{1,2})?$/.test(amount.toString())) {
    res.status(400).json({
      success: false,
      error: '金额最多保留2位小数'
    } as ApiResponse);
    return;
  }

  next();
};

/**
 * 验证订单ID
 */
export const validateOrderId = (req: Request, res: Response, next: NextFunction): void => {
  const { orderId } = req.params;

  if (!orderId || orderId.trim().length === 0) {
    res.status(400).json({
      success: false,
      error: '订单ID不能为空'
    } as ApiResponse);
    return;
  }

  // 订单ID格式验证（可以根据实际格式调整）
  if (orderId.length > 100) {
    res.status(400).json({
      success: false,
      error: '订单ID格式不正确'
    } as ApiResponse);
    return;
  }

  next();
};

/**
 * 通用错误处理中间件
 */
export const errorHandler = (error: Error, req: Request, res: Response, next: NextFunction): void => {
  console.error('API错误:', error);

  // 如果响应已经发送，交给默认错误处理器
  if (res.headersSent) {
    return next(error);
  }

  res.status(500).json({
    success: false,
    error: process.env.NODE_ENV === 'production' ? '服务器内部错误' : error.message
  } as ApiResponse);
};

import rateLimit from 'express-rate-limit';

// 创建订单限流：每个IP每分钟最多5次
export const createOrderLimiter = rateLimit({
  windowMs: 60 * 1000, // 1分钟
  max: 5, // 最多5次请求
  message: {
    success: false,
    error: '请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 查询状态限流：每个IP每分钟最多30次
export const queryStatusLimiter = rateLimit({
  windowMs: 60 * 1000, // 1分钟
  max: 30, // 最多30次请求
  message: {
    success: false,
    error: '查询过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 管理接口限流：每个IP每分钟最多10次
export const adminApiLimiter = rateLimit({
  windowMs: 60 * 1000, // 1分钟
  max: 10, // 最多10次请求
  message: {
    success: false,
    error: '管理接口访问过于频繁'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 通用API限流：每个IP每分钟最多100次
export const generalLimiter = rateLimit({
  windowMs: 60 * 1000, // 1分钟
  max: 100, // 最多100次请求
  message: {
    success: false,
    error: 'API访问过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

export const rateLimiter = {
  createOrder: createOrderLimiter,
  queryStatus: queryStatusLimiter,
  adminApi: adminApiLimiter,
  general: generalLimiter
};

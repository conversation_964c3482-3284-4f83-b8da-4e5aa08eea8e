import { DataTypes, Model, Optional } from 'sequelize';
import sequelize from '../config/database';
import { OrderStatus } from '../types';

interface OrderAttributes {
  id: string;
  email: string;
  amount: number;
  status: OrderStatus;
  wechatOrderId?: string;
  qrCodeUrl?: string;
  expireTime: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface OrderCreationAttributes extends Optional<OrderAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

class Order extends Model<OrderAttributes, OrderCreationAttributes> implements OrderAttributes {
  public id!: string;
  public email!: string;
  public amount!: number;
  public status!: OrderStatus;
  public wechatOrderId?: string;
  public qrCodeUrl?: string;
  public expireTime!: Date;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 检查订单是否过期
  public isExpired(): boolean {
    return new Date() > this.expireTime;
  }

  // 检查订单是否可以支付
  public canPay(): boolean {
    return this.status === OrderStatus.PENDING && !this.isExpired();
  }
}

Order.init(
  {
    id: {
      type: DataTypes.STRING(50),
      primaryKey: true,
      allowNull: false
    },
    email: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        isEmail: true
      }
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: 0.01
      }
    },
    status: {
      type: DataTypes.ENUM(...Object.values(OrderStatus)),
      allowNull: false,
      defaultValue: OrderStatus.PENDING
    },
    wechatOrderId: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    qrCodeUrl: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    expireTime: {
      type: DataTypes.DATE,
      allowNull: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  },
  {
    sequelize,
    modelName: 'Order',
    tableName: 'orders',
    timestamps: true,
    indexes: [
      {
        fields: ['email']
      },
      {
        fields: ['status']
      },
      {
        fields: ['wechatOrderId']
      },
      {
        fields: ['expireTime']
      }
    ]
  }
);

export default Order;

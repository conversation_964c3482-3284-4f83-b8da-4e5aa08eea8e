version: '3.8'

services:
  # 微信支付后端服务
  wechat-pay-backend:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: wechat-pay-backend
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
    env_file:
      - ../.env
    volumes:
      - ../certs:/app/certs:ro
      - ../logs:/app/logs
    depends_on:
      - mysql
    networks:
      - wechat-pay-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: wechat-pay-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "3306:3306"
    networks:
      - wechat-pay-network
    command: --default-authentication-plugin=mysql_native_password

  # Redis (可选，用于缓存和会话)
  redis:
    image: redis:7-alpine
    container_name: wechat-pay-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - wechat-pay-network
    command: redis-server --appendonly yes

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: wechat-pay-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - wechat-pay-backend
    networks:
      - wechat-pay-network

volumes:
  mysql_data:
  redis_data:

networks:
  wechat-pay-network:
    driver: bridge

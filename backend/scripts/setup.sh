#!/bin/bash

# 微信支付后端服务安装脚本

echo "🚀 开始安装微信支付后端服务..."

# 检查Node.js版本
if ! command -v node &> /dev/null; then
    echo "❌ 请先安装 Node.js (版本 >= 16)"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "❌ Node.js 版本过低，请升级到 16 或更高版本"
    exit 1
fi

echo "✅ Node.js 版本检查通过: $(node -v)"

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装"
    exit 1
fi

echo "✅ npm 版本: $(npm -v)"

# 安装依赖
echo "📦 安装依赖包..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败"
    exit 1
fi

echo "✅ 依赖安装完成"

# 创建必要的目录
echo "📁 创建目录结构..."
mkdir -p certs
mkdir -p logs
mkdir -p dist

# 复制环境变量配置文件
if [ ! -f .env ]; then
    echo "📝 创建环境变量配置文件..."
    cp .env.example .env
    echo "⚠️  请编辑 .env 文件，填入实际的配置信息"
else
    echo "✅ 环境变量配置文件已存在"
fi

# 设置证书目录权限
chmod 700 certs
echo "🔒 证书目录权限设置完成"

# 编译TypeScript
echo "🔨 编译TypeScript..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ TypeScript 编译失败"
    exit 1
fi

echo "✅ TypeScript 编译完成"

echo ""
echo "🎉 安装完成！"
echo ""
echo "📋 下一步操作："
echo "1. 编辑 .env 文件，填入微信支付配置"
echo "2. 将微信支付证书文件放入 certs/ 目录"
echo "3. 创建数据库: CREATE DATABASE wechat_pay;"
echo "4. 启动服务: npm run dev"
echo ""
echo "📚 详细说明请查看 README.md"

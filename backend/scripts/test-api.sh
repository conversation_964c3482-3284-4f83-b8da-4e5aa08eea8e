#!/bin/bash

# API测试脚本

API_BASE_URL="http://localhost:3001"
TEST_EMAIL="<EMAIL>"
TEST_AMOUNT="29.99"

echo "🧪 开始测试微信支付API..."

# 测试健康检查
echo "1. 测试健康检查接口..."
response=$(curl -s -w "%{http_code}" -o /tmp/health_response.json "$API_BASE_URL/health")
http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    echo "✅ 健康检查通过"
    cat /tmp/health_response.json | jq .
else
    echo "❌ 健康检查失败 (HTTP $http_code)"
    cat /tmp/health_response.json
    exit 1
fi

echo ""

# 测试创建订单
echo "2. 测试创建支付订单..."
create_response=$(curl -s -w "%{http_code}" -o /tmp/create_response.json \
    -X POST \
    -H "Content-Type: application/json" \
    -d "{\"email\":\"$TEST_EMAIL\",\"amount\":$TEST_AMOUNT}" \
    "$API_BASE_URL/api/payment/create")

create_http_code="${create_response: -3}"

if [ "$create_http_code" = "200" ]; then
    echo "✅ 创建订单成功"
    cat /tmp/create_response.json | jq .
    
    # 提取订单ID
    order_id=$(cat /tmp/create_response.json | jq -r '.data.orderId')
    echo "📝 订单ID: $order_id"
else
    echo "❌ 创建订单失败 (HTTP $create_http_code)"
    cat /tmp/create_response.json
    exit 1
fi

echo ""

# 测试查询订单状态
if [ ! -z "$order_id" ] && [ "$order_id" != "null" ]; then
    echo "3. 测试查询订单状态..."
    status_response=$(curl -s -w "%{http_code}" -o /tmp/status_response.json \
        "$API_BASE_URL/api/payment/status/$order_id")
    
    status_http_code="${status_response: -3}"
    
    if [ "$status_http_code" = "200" ]; then
        echo "✅ 查询状态成功"
        cat /tmp/status_response.json | jq .
    else
        echo "❌ 查询状态失败 (HTTP $status_http_code)"
        cat /tmp/status_response.json
    fi
else
    echo "⚠️  跳过状态查询测试（订单ID无效）"
fi

echo ""

# 测试错误处理
echo "4. 测试错误处理..."

# 测试无效邮箱
echo "4.1 测试无效邮箱..."
invalid_email_response=$(curl -s -w "%{http_code}" -o /tmp/invalid_email_response.json \
    -X POST \
    -H "Content-Type: application/json" \
    -d "{\"email\":\"invalid-email\",\"amount\":$TEST_AMOUNT}" \
    "$API_BASE_URL/api/payment/create")

invalid_email_http_code="${invalid_email_response: -3}"

if [ "$invalid_email_http_code" = "400" ]; then
    echo "✅ 无效邮箱错误处理正确"
else
    echo "❌ 无效邮箱错误处理异常 (HTTP $invalid_email_http_code)"
fi

# 测试无效金额
echo "4.2 测试无效金额..."
invalid_amount_response=$(curl -s -w "%{http_code}" -o /tmp/invalid_amount_response.json \
    -X POST \
    -H "Content-Type: application/json" \
    -d "{\"email\":\"$TEST_EMAIL\",\"amount\":-1}" \
    "$API_BASE_URL/api/payment/create")

invalid_amount_http_code="${invalid_amount_response: -3}"

if [ "$invalid_amount_http_code" = "400" ]; then
    echo "✅ 无效金额错误处理正确"
else
    echo "❌ 无效金额错误处理异常 (HTTP $invalid_amount_http_code)"
fi

# 测试不存在的订单
echo "4.3 测试查询不存在的订单..."
notfound_response=$(curl -s -w "%{http_code}" -o /tmp/notfound_response.json \
    "$API_BASE_URL/api/payment/status/NONEXISTENT_ORDER")

notfound_http_code="${notfound_response: -3}"

if [ "$notfound_http_code" = "500" ]; then
    echo "✅ 不存在订单错误处理正确"
else
    echo "❌ 不存在订单错误处理异常 (HTTP $notfound_http_code)"
fi

echo ""
echo "🎉 API测试完成！"

# 清理临时文件
rm -f /tmp/*_response.json

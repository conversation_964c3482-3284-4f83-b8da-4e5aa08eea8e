#!/bin/bash

# 微信支付后端服务部署脚本

set -e

echo "🚀 开始部署微信支付后端服务..."

# 检查环境
if [ ! -f .env ]; then
    echo "❌ .env 文件不存在，请先配置环境变量"
    exit 1
fi

if [ ! -d certs ]; then
    echo "❌ certs 目录不存在，请先创建并放入微信支付证书"
    exit 1
fi

if [ ! -f certs/apiclient_key.pem ]; then
    echo "❌ 微信支付私钥文件不存在: certs/apiclient_key.pem"
    exit 1
fi

# 构建应用
echo "🔨 构建应用..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ 构建失败"
    exit 1
fi

echo "✅ 构建完成"

# 检查PM2是否安装
if ! command -v pm2 &> /dev/null; then
    echo "📦 安装 PM2..."
    npm install -g pm2
fi

# 停止现有进程
echo "🛑 停止现有进程..."
pm2 stop wechat-pay-backend 2>/dev/null || true
pm2 delete wechat-pay-backend 2>/dev/null || true

# 启动新进程
echo "🚀 启动新进程..."
pm2 start ecosystem.config.js --env production

# 保存PM2配置
pm2 save

# 设置开机自启
pm2 startup

echo "✅ 部署完成！"
echo ""
echo "📊 服务状态:"
pm2 status

echo ""
echo "📋 有用的命令:"
echo "  查看日志: pm2 logs wechat-pay-backend"
echo "  重启服务: pm2 restart wechat-pay-backend"
echo "  停止服务: pm2 stop wechat-pay-backend"
echo "  监控面板: pm2 monit"

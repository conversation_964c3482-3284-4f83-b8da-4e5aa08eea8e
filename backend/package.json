{"name": "wechat-pay-backend", "version": "1.0.0", "description": "微信支付后端服务", "main": "dist/index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "wechatpay-node-v3": "^1.4.4", "mysql2": "^3.6.5", "sequelize": "^6.35.1", "uuid": "^9.0.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5"}, "devDependencies": {"@types/node": "^20.10.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/uuid": "^9.0.7", "typescript": "^5.3.2", "ts-node-dev": "^2.0.0", "jest": "^29.7.0", "@types/jest": "^29.5.8"}, "keywords": ["wechat", "payment", "api"], "author": "", "license": "MIT"}
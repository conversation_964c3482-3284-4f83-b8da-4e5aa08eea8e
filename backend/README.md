# 微信支付后端服务

## 🚀 快速开始

### 1. 安装依赖

```bash
cd backend
npm install
```

### 2. 配置环境变量

复制 `.env.example` 为 `.env` 并填入实际配置：

```bash
cp .env.example .env
```

需要配置的关键参数：
- `WECHAT_APPID`: 微信应用ID
- `WECHAT_MCHID`: 微信商户号
- `WECHAT_PRIVATE_KEY_PATH`: 微信支付私钥文件路径
- `WECHAT_CERTIFICATE_SERIAL_NUMBER`: 证书序列号
- `WECHAT_API_V3_KEY`: APIv3密钥
- 数据库配置

### 3. 准备微信支付证书

将微信支付证书文件放在 `backend/certs/` 目录下：
- `apiclient_key.pem` - 商户私钥
- `apiclient_cert.pem` - 商户证书

### 4. 创建数据库

```sql
CREATE DATABASE wechat_pay CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 5. 启动服务

开发模式：
```bash
npm run dev
```

生产模式：
```bash
npm run build
npm start
```

## 📋 API接口

### 创建支付订单
```
POST /api/payment/create
Content-Type: application/json

{
  "email": "<EMAIL>",
  "amount": 29.99
}
```

### 查询订单状态
```
GET /api/payment/status/:orderId
```

### 微信支付回调
```
POST /api/payment/notify
```

## 🔧 配置说明

### 微信支付配置步骤

1. **申请微信商户号**
   - 访问 https://pay.weixin.qq.com
   - 提交资质材料等待审核

2. **获取配置信息**
   - APPID: 微信公众号/小程序的应用ID
   - MCHID: 商户号
   - API密钥: 在商户平台设置
   - 证书: 下载商户证书文件

3. **配置回调URL**
   - 在微信商户平台配置支付回调URL
   - 格式: `https://yourdomain.com/api/payment/notify`

### 数据库表结构

系统会自动创建以下表：
- `orders`: 订单表，存储支付订单信息

## 🛡️ 安全注意事项

1. **证书安全**
   - 私钥文件权限设置为 600
   - 不要将证书文件提交到版本控制

2. **环境变量**
   - 生产环境使用环境变量而非 .env 文件
   - API密钥等敏感信息加密存储

3. **HTTPS**
   - 生产环境必须使用HTTPS
   - 微信支付回调要求HTTPS

## 📊 监控和日志

- 服务健康检查: `GET /health`
- 日志级别可通过 `LOG_LEVEL` 环境变量配置
- 建议配置日志收集和监控报警

## 🔄 部署建议

1. **反向代理**
   - 使用 Nginx 作为反向代理
   - 配置SSL证书

2. **进程管理**
   - 使用 PM2 管理Node.js进程
   - 配置自动重启和负载均衡

3. **数据库**
   - 配置数据库连接池
   - 定期备份数据库

## 🐛 故障排除

### 常见问题

1. **证书路径错误**
   - 检查 `WECHAT_PRIVATE_KEY_PATH` 是否正确
   - 确认证书文件存在且可读

2. **数据库连接失败**
   - 检查数据库配置
   - 确认数据库服务运行正常

3. **微信支付API调用失败**
   - 检查商户号和APPID是否匹配
   - 确认API密钥配置正确

### 调试模式

设置环境变量启用详细日志：
```bash
NODE_ENV=development
LOG_LEVEL=debug
```
